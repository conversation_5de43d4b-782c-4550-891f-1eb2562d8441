<?php

namespace Tests\Feature\Logic;

use App\Constants\BusinessConst;
use App\Constants\OrderConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BaseErr;
use App\ErrCode\OrderErr;
use App\ErrCode\TemplateErr;
use App\Logic\Inner\OrderLogic;
use App\Models\BusinessCategoryModel;
use App\Models\BusinessFieldModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use App\Service\AppraiserService;
use App\Service\BusinessCategoryService;
use App\Service\BusinessService;
use App\Service\OrderCommissionService;
use App\Service\OrderService;
use App\Service\OrderSubmitService;
use App\Service\TemplateService;
use Mockery;
use Tests\TestCase;

class OrderLogicTest extends TestCase
{
    protected $orderLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderLogic = OrderLogic::getInstance();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }

    /**
     * Test successful order creation
     */
    public function testCreateOrderSuccess()
    {
        // Mock dependencies
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('createOrder')->andReturn(1);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $commissionServiceMock = Mockery::mock(OrderCommissionService::class);
        $commissionServiceMock->shouldReceive('orderCommission')->andReturn(true);
        $this->app->instance(OrderCommissionService::class, $commissionServiceMock);

        $appraiserServiceMock = Mockery::mock(AppraiserService::class);
        $appraiserServiceMock->shouldReceive('detail')->with(11740300)->andReturn(['nickname' => 'Test Appraiser']);
        $this->app->instance(AppraiserService::class, $appraiserServiceMock);

        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn($this->createBusinessCategoryMock());
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $templateServiceMock = Mockery::mock(TemplateService::class);
        $templateServiceMock->shouldReceive('getTemplateDetail')->andReturn([
            'isDeleted' => false,
            'bizType' => 1,
            'fields' => [
                ['fieldKey' => 'testField', 'fieldTitle' => 'Test Field', 'outputType' => OrderConst::OUTPUT_TYPE_ORDER_ITEM],
                ['fieldKey' => 'orderField', 'fieldTitle' => 'Order Field', 'outputType' => OrderConst::OUTPUT_TYPE_ORDER]
            ]
        ]);
        $templateServiceMock->shouldReceive('getTemplateById')->andReturn($this->createBusinessTemplateFieldMock());
        $this->app->instance(TemplateService::class, $templateServiceMock);

        $params = new OrderParams();
        $params->businessId = 1;
        $params->categoryIdentifier = 902;
        $params->appraiserId = 11740300;
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => [],
                'remark' => 'Test remark',
                'testField' => 'Test value'
            ]
        ];
        $params->order = ['orderField' => 'Order value'];

        $result = $this->orderLogic->create($params);

        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals(11740300, $result['appraiserId']);
        $this->assertEquals('Test Appraiser', $result['appraiserName']);
    }

    /**
     * Test order creation with missing business ID
     */
    public function testCreateOrderMissingBusinessId()
    {
        $params = new OrderParams();
        $params->categoryIdentifier = 902;
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR);
        $this->orderLogic->create($params);
    }

    /**
     * Test order creation with missing category identifier
     */
    public function testCreateOrderMissingCategoryIdentifier()
    {
        $params = new OrderParams();
        $params->businessId = 1;
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectExceptionCode(OrderErr::CATEGORY_NOT_CONFIGURED_CORRECTLY);
        $this->orderLogic->create($params);
    }

    /**
     * Test order creation with missing items
     */
    public function testCreateOrderMissingItems()
    {
        $params = new OrderParams();
        $params->businessId = 1;
        $params->categoryIdentifier = 902;

        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR);
        $this->orderLogic->create($params);
    }

    /**
     * Test order creation with invalid appraiser
     */
    public function testCreateOrderInvalidAppraiser()
    {
        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn($this->createBusinessCategoryMock());
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $templateServiceMock = Mockery::mock(TemplateService::class);
        $templateServiceMock->shouldReceive('getTemplateDetail')->andReturn([
            'isDeleted' => false,
            'bizType' => 1,
            'fields' => []
        ]);
        $templateServiceMock->shouldReceive('getTemplateById')->andReturn($this->createBusinessTemplateFieldMock());
        $this->app->instance(TemplateService::class, $templateServiceMock);

        $params = new OrderParams();
        $params->businessId = 1;
        $params->categoryIdentifier = 902;
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_DISPATCH);
        $this->orderLogic->create($params);
    }

    /**
     * Test get order parameters
     */
    public function testGetParams()
    {
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($this->createOrderMock());
        $orderServiceMock->shouldReceive('getOrderSubmitParams')->andReturn(['param1' => 'value1']);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $result = $this->orderLogic->getParams('test_uri');
        $this->assertArrayHasKey('inputParams', $result);
        $this->assertEquals(['param1' => 'value1'], $result['inputParams']);
    }

    /**
     * Test get params with non-existent order
     */
    public function testGetParamsOrderNotExist()
    {
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn(null);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST);
        $this->orderLogic->getParams('test_uri');
    }

    /**
     * Test order detail
     */
    public function testDetail()
    {
        $order = $this->createOrderMock();

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('getOrderInputData')->andReturn(['input' => 'data']);
        $orderServiceMock->shouldReceive('getOrderOutputData')->andReturn(['output' => 'data']);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $businessServiceMock = Mockery::mock(BusinessService::class);
        $businessServiceMock->shouldReceive('getOneById')->andReturn(['name' => 'Test Business']);
        $this->app->instance(BusinessService::class, $businessServiceMock);

        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn((object)['category_name' => 'Test Category']);
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $result = $this->orderLogic->detail('test_uri');
        $this->assertEquals('test_uri', $result['uri']);
        $this->assertEquals(1, $result['businessId']);
        $this->assertEquals('Test Business', $result['businessName']);
        $this->assertEquals('Test Category', $result['categoryName']);
        $this->assertArrayHasKey('input', $result);
        $this->assertArrayHasKey('output', $result);
    }

    /**
     * Test order detail with non-existent order
     */
    public function testDetailOrderNotExist()
    {
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn(null);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST);
        $this->orderLogic->detail('test_uri');
    }

    /**
     * Test simple detail
     */
    public function testSimpleDetail()
    {
        $order = $this->createOrderMock();

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('getOrderInputData')->andReturn(['input' => 'data']);
        $orderServiceMock->shouldReceive('getOrderOutputData')->andReturn([
            'items' => [[['fieldKey' => 'key1', 'fieldValue' => 'value1']]],
            'order' => [['fieldKey' => 'orderKey', 'fieldValue' => 'orderValue']]
        ]);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $businessServiceMock = Mockery::mock(BusinessService::class);
        $businessServiceMock->shouldReceive('getOneById')->andReturn(['name' => 'Test Business']);
        $this->app->instance(BusinessService::class, $businessServiceMock);

        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn((object)['category_name' => 'Test Category']);
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $result = $this->orderLogic->simpleDetail('test_uri');
        $this->assertEquals('test_uri', $result['uri']);
        $this->assertArrayHasKey('output', $result);
        $this->assertArrayNotHasKey('input', $result);
        $this->assertEquals(['key1' => 'value1'], $result['output']['items'][0]);
        $this->assertEquals(['orderKey' => 'orderValue'], $result['output']['order']);
    }

    /**
     * Test batch simple detail
     */
    public function testBatchSimpleDetail()
    {
        $order = $this->createOrderMock();

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('invalid_uri')->andReturn(null);
        $orderServiceMock->shouldReceive('getOrderInputData')->andReturn(['input' => 'data']);
        $orderServiceMock->shouldReceive('getOrderOutputData')->andReturn([
            'items' => [[['fieldKey' => 'key1', 'fieldValue' => 'value1']]],
            'order' => [['fieldKey' => 'orderKey', 'fieldValue' => 'orderValue']]
        ]);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $businessServiceMock = Mockery::mock(BusinessService::class);
        $businessServiceMock->shouldReceive('getOneById')->andReturn(['name' => 'Test Business']);
        $this->app->instance(BusinessService::class, $businessServiceMock);

        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn((object)['category_name' => 'Test Category']);
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $result = $this->orderLogic->batchSimpleDetail(['test_uri', 'invalid_uri']);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('test_uri', $result['list']);
        $this->assertArrayNotHasKey('invalid_uri', $result['list']);
    }

    /**
     * Test assign appraiser success
     */
    public function testAssignAppraiserSuccess()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('updateById')->with(1, Mockery::any())->andReturn(true);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $appraiserServiceMock = Mockery::mock(AppraiserService::class);
        $appraiserServiceMock->shouldReceive('detail')->with(11740300)->andReturn(['id' => 11740300]);
        $this->app->instance(AppraiserService::class, $appraiserServiceMock);

        $result = $this->orderLogic->assignAppraiser('test_uri', 11740300);
        $this->assertEquals(['state' => 1], $result);
    }

    /**
     * Test assign appraiser with non-existent order
     */
    public function testAssignAppraiserOrderNotExist()
    {
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn(null);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST);
        $this->orderLogic->assignAppraiser('test_uri', 11740300);
    }

    /**
     * Test assign appraiser with invalid state
     */
    public function testAssignAppraiserInvalidState()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_COMPLETE);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_ASSIGN_OR_MODIFY);
        $this->orderLogic->assignAppraiser('test_uri', 11740300);
    }

    /**
     * Test assign appraiser with non-existent appraiser
     */
    public function testAssignAppraiserNotExist()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $appraiserServiceMock = Mockery::mock(AppraiserService::class);
        $appraiserServiceMock->shouldReceive('detail')->with(1)->andReturn(null);
        $this->app->instance(AppraiserService::class, $appraiserServiceMock);

        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST);
        $this->orderLogic->assignAppraiser('test_uri', 1);
    }

    /**
     * Test cancel order success
     */
    public function testCancelOrderSuccess()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('updateById')->with(1, ['state' => OrderConst::ORDER_STATE_CANCEL])->andReturn(true);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $result = $this->orderLogic->cancel('test_uri');
        $this->assertEquals([], $result);
    }

    /**
     * Test cancel order with non-existent order
     */
    public function testCancelOrderNotExist()
    {
        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn(null);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST);
        $this->orderLogic->cancel('test_uri');
    }

    /**
     * Test cancel order with invalid state
     */
    public function testCancelOrderInvalidState()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_COMPLETE);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_CANCEL);
        $this->orderLogic->cancel('test_uri');
    }

    /**
     * Test reject order success
     */
    public function testRejectOrderSuccess()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_COMPLETE);
        $order->shouldReceive('getAttribute')->with('input_template_id')->andReturn(1);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('updateById')->andReturn(true);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $appraiserServiceMock = Mockery::mock(AppraiserService::class);
        $appraiserServiceMock->shouldReceive('detail')->with(11740300)->andReturn(['id' => 11740300]);
        $this->app->instance(AppraiserService::class, $appraiserServiceMock);

        $templateField = $this->createBusinessTemplateFieldMock();
        $templateField->shouldReceive('getAttribute')->with('field_id')->andReturn(1);
        $this->mockModel(BusinessTemplateFieldModel::class, 'where', $templateField);

        $field = $this->createBusinessFieldMock();
        $field->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $field->shouldReceive('getAttribute')->with('name')->andReturn('rejectReason');
        $field->shouldReceive('getAttribute')->with('field_key')->andReturn('rejectReason');
        $field->shouldReceive('getAttribute')->with('field_type')->andReturn('text');
        $this->mockModel(BusinessFieldModel::class, 'where', $field);

        $orderItem = $this->createOrderItemMock();
        $orderItem->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $orderItem->shouldReceive('getAttribute')->with('order_id')->andReturn(1);
        $this->mockModel(OrderItemModel::class, 'where', $orderItem);

        $this->mockModel(OrderItemFieldModel::class, 'insert', true);

        $result = $this->orderLogic->reject('test_uri', 11740300, 'Test reject reason');
        $this->assertArrayHasKey('result', $result);
        $this->assertTrue($result['result']);
    }

    /**
     * Test list orders
     */
    public function testListOrders()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_COMPLETE);

        $orderCollection = collect([$order]);
        $this->mockModel(OrderModel::class, 'where', $orderCollection);

        $businessServiceMock = Mockery::mock(BusinessService::class);
        $businessServiceMock->shouldReceive('getListByIds')->andReturn(collect([['id' => 1, 'name' => 'Test Business']]));
        $this->app->instance(BusinessService::class, $businessServiceMock);

        $businessCategoryServiceMock = Mockery::mock(BusinessCategoryService::class);
        $businessCategoryServiceMock->shouldReceive('getCategoryByIdentifier')->andReturn(['category_name' => 'Test Category']);
        $this->app->instance(BusinessCategoryService::class, $businessCategoryServiceMock);

        $params = ['page' => 1, 'pageSize' => 10];
        $result = $this->orderLogic->list($params);

        $this->assertArrayHasKey('list', $result);
        $this->assertEquals(2, $result['page']);
        $this->assertEquals(10, $result['pageSize']);
        $this->assertFalse($result['isEnd']);
        $this->assertEquals('test_uri', $result['list'][0]['uri']);
    }

    /**
     * Test submit order
     */
    public function testSubmitOrder()
    {
        $order = $this->createOrderMock();
        $order->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_WAIT_IDENTIFY);

        $orderServiceMock = Mockery::mock(OrderService::class);
        $orderServiceMock->shouldReceive('getOrderByUri')->with('test_uri')->andReturn($order);
        $orderServiceMock->shouldReceive('getOrderSubmitParams')->andReturn([
            ['fieldKey' => 'field1', 'fieldType' => 'text', 'outputType' => OrderConst::OUTPUT_TYPE_ORDER],
            ['fieldKey' => 'field2', 'fieldType' => 'text', 'outputType' => OrderConst::OUTPUT_TYPE_ORDER_ITEM]
        ]);
        $orderServiceMock->shouldReceive('updateById')->andReturn(true);
        $this->app->instance(OrderService::class, $orderServiceMock);

        $orderSubmitServiceMock = Mockery::mock(OrderSubmitService::class);
        $orderSubmitServiceMock->shouldReceive('switchCheck')->andReturnArg(0);
        $this->app->instance(OrderSubmitService::class, $orderSubmitServiceMock);

        $orderCommissionServiceMock = Mockery::mock(OrderCommissionService::class);
        $orderCommissionServiceMock->shouldReceive('orderCommissionSettlement')->andReturn(true);
        $this->app->instance(OrderCommissionService::class, $orderCommissionServiceMock);

        $orderItem = $this->createOrderItemMock();
        $orderItem->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $orderItem->shouldReceive('getAttribute')->with('order_id')->andReturn(1);
        $orderItemCollection = collect([$orderItem]);
        $this->mockModel(OrderItemModel::class, 'where', $orderItemCollection);
        $this->mockModel(OrderItemFieldModel::class, 'where', collect([]));
        $this->mockModel(OrderItemFieldModel::class, 'insert', true);
        $this->mockModel(OrderItemFieldModel::class, 'delete', true);

        $params = [
            'uri' => 'test_uri',
            'identResult' => 'result',
            'identTruth' => 1,
            'field1' => 'value1',
            'items' => [['field2' => 'value2']]
        ];

        $result = $this->orderLogic->submit($params);
        $this->assertEquals([], $result);
    }

    /**
     * Helper method to create a mock BusinessCategoryModel
     */
    private function createBusinessCategoryMock()
    {
        $mock = Mockery::mock(BusinessCategoryModel::class);
        $mock->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('input_template_id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('output_template_id')->andReturn(1);
        return $mock;
    }

    /**
     * Helper method to create a mock BusinessTemplateFieldModel
     */
    private function createBusinessTemplateFieldMock()
    {
        $mock = Mockery::mock(BusinessTemplateFieldModel::class);
        $mock->shouldReceive('getAttribute')->with('is_deleted')->andReturn(false);
        $mock->shouldReceive('getAttribute')->with('biz_type')->andReturn(2);
        return $mock;
    }

    /**
     * Helper method to create a mock BusinessFieldModel
     */
    private function createBusinessFieldMock()
    {
        return Mockery::mock(BusinessFieldModel::class);
    }

    /**
     * Helper method to create a mock OrderModel
     */
    private function createOrderMock()
    {
        $mock = Mockery::mock(OrderModel::class);
        $mock->shouldReceive('getAttribute')->with('uri')->andReturn('test_uri');
        $mock->shouldReceive('getAttribute')->with('business_id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('cover')->andReturn('cover.jpg');
        $mock->shouldReceive('getAttribute')->with('business_no')->andReturn('BN123');
        $mock->shouldReceive('getAttribute')->with('business_master_no')->andReturn('BMN123');
        $mock->shouldReceive('getAttribute')->with('category_identifier')->andReturn(902);
        $mock->shouldReceive('getAttribute')->with('category_id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('state')->andReturn(OrderConst::ORDER_STATE_COMPLETE);
        $mock->shouldReceive('getAttribute')->with('end_time')->andReturn('2023-01-01 00:00:00');
        $mock->shouldReceive('getAttribute')->with('input_template_id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('output_template_id')->andReturn(1);
        $mock->shouldReceive('getAttribute')->with('userinfo_id')->andReturn(11740300);
        $mock->shouldReceive('getAttribute')->with('detail_json')->andReturn('{}');
        $mock->shouldReceive('getAttribute')->with('accept_time')->andReturn(strtotime('2023-01-01 10:00:00'));
        $mock->shouldReceive('getAttribute')->with('create_time')->andReturn(strtotime('2023-01-01 09:00:00'));
        $mock->shouldReceive('getAttribute')->with('id')->andReturn(1);
        return $mock;
    }

    /**
     * Helper method to create a mock OrderItemModel
     */
    private function createOrderItemMock()
    {
        return Mockery::mock(OrderItemModel::class);
    }

    /**
     * Helper method to mock model queries
     */
    private function mockModel($class, $method, $returnValue)
    {
        $mock = Mockery::mock($class);
        $mock->shouldReceive('query')->andReturnSelf();
        $mock->shouldReceive($method)->andReturn($returnValue);
        if ($method !== 'insert' && $method !== 'delete') {
            $mock->shouldReceive('get')->andReturn($returnValue);
            $mock->shouldReceive('first')->andReturn($returnValue instanceof \Illuminate\Support\Collection ? $returnValue->first() : $returnValue);
        }
        $this->app->instance($class, $mock);
        return $mock;
    }
} 